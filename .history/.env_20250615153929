# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# The following `prisma+postgres` URL is similar to the URL produced by running a local Prisma Postgres 
# server with the `prisma dev` CLI command, when not choosing any non-default ports or settings. The API key, unlike the 
# one found in a remote Prisma Postgres URL, does not contain any sensitive information.

DATABASE_URL="prisma+postgres://localhost:51213/?api_key=eyJkYXRhYmFzZVVybCI6InBvc3RncmVzOi8vcG9zdGdyZXM6cG9zdGdyZXNAbG9jYWxob3N0OjUxMjE0L3RlbXBsYXRlMT9zc2xtb2RlPWRpc2FibGUmY29ubmVjdGlvbl9saW1pdD0xJmNvbm5lY3RfdGltZW91dD0wJm1heF9pZGxlX2Nvbm5lY3Rpb25fbGlmZXRpbWU9MCZwb29sX3RpbWVvdXQ9MCZzaW5nbGVfdXNlX2Nvbm5lY3Rpb25zPXRydWUmc29ja2V0X3RpbWVvdXQ9MCIsIm5hbWUiOiJkZWZhdWx0Iiwic2hhZG93RGF0YWJhc2VVcmwiOiJwb3N0Z3JlczovL3Bvc3RncmVzOnBvc3RncmVzQGxvY2FsaG9zdDo1MTIxNS90ZW1wbGF0ZTE_c3NsbW9kZT1kaXNhYmxlJmNvbm5lY3Rpb25fbGltaXQ9MSZjb25uZWN0X3RpbWVvdXQ9MCZtYXhfaWRsZV9jb25uZWN0aW9uX2xpZmV0aW1lPTAmcG9vbF90aW1lb3V0PTAmc2luZ2xlX3VzZV9jb25uZWN0aW9ucz10cnVlJnNvY2tldF90aW1lb3V0PTAifQ"

# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

NODE_ENV=production
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ai-appdev"
JWT_SECRET="VLEYqZB+A6ISsv3NViNcaqMJma2VJvKnAzS4VfU6LZ8="
NEXTAUTH_SECRET="VLEYqZB+A6ISsv3NViNcaqMJma2VJvKnAzS4VfU6LZ8=" # Added by `npx auth`. Read more: https://cli.authjs.dev
NEXTAUTH_URL="http://localhost:3080"
AUTH_URL="http://localhost:3080/"
AUTH_SECRET="Hxm7rdOD8gYZkh4AAmt/jb1n4Dl9A5gJsxww7BQrB15="
AUTH_GITHUB_CALLBACK_URL="https://http://localhost:3080/api/auth/callback/github"

NEXT_PUBLIC_AUTH_SECRET="Hxm7rdOD8gYZkh4AAmt/jb1n4Dl9A5gJsxww7BQrB15="

# URLs
NEXT_PUBLIC_APP_URL="http://localhost:3080"

# Faker
FAKER_LOCALE=zu_ZA

# Email (SMTP)
EMAIL_SERVER_HOST=mail.weevolve-ai.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=&nM^2&d3O7a$
EMAIL_FROM=<EMAIL>
EMAIL_SERVER_SECURE=true

# AI
# Groq
GROQ_API_URL=https://api.groq.com/openai/v1
GROQ_API_KEY=********************************************************
# TOGETHER
TOGETHER_API_KEY=2c464b139fe698460948c11980192a9f6c52f8cceb463e02327dfedc54c05ffb
TOGETHER_API_URL=https://api.together.xyz/v1
# OPENAI
OPENAI_API_KEY=********************************************************
NEXT_PUBLIC_OPENAI_API_KEY=********************************************************
OPENAI_API_URL=https://api.openai.com/v1
# Anthropic
ANTHROPIC_API_KEY=************************************************************************************************************
ANTHROPIC_API_URL=https://api.anthropic.com/v1
# Hugging Face
HUGGING_FACE_TOKEN=hf_5f0158dab0c5ef9e4e5c6c0c5c6d0d5d1
# Frendli.ai
FRENDLI_TOKEN=flp_6fYFBVUis74DhWoX4lbYU2jzpKte0wJzOKZVisgrTBvwd4
# GOOGLE
GOOGLE_API_KEY=AIzaSyANKcett9bHSdtIVQN8tEYe0t2j6XOCacE
# COHERE 
COHERE_API_KEY=A98SPpiXXcXmehyV6CcyguZ3aFOu9CheW5DfQkDx


# AI TOOLS
# WOLFRAM
WOLFRAM_APP_ID=GH27P3-AV65TG5599
# SERP
NEXT_PUBLIC_SERP_API_KEY=08f15c5fcbaf0363e85e3bd2839ec94205c74ceef952de32cdb52037298ff6d6
#SERPER
NEXT_PUBLIC_SERPER_API_KEY=8e5d7a56c8a94bebf44a7ef81185b4d2c169b1f6
# EXA
EXA_API_KEY=b75f99cc-bf9d-43ab-9857-fdebcb992581
# FIRECRAWL
FIRECRAWL_API_KEY=fc-312c9c95bfc14b95ae84f603bc98bf08
# BROWSERBASE
BROWSERBASE_API_KEY=bb_live_cf2mY0OG4gZD0bxRYRAcAx5XpWw
BROWSERBASE_PROJECT_ID=5de04e0c-8c83-4c86-9056-3520b82813eb
# WEVIATE
WEAVIATE_API_URL=https://weaviate.weevolve-ai.com/
WEAVIATE_API_KEY=6yjXP9oGvxkA4vsecHUz9JVgIXUZthk7
WEAVIATE_API_HOST=weaviate.weevolve-ai.com
WEAVIATE_API_PORT=8080
WEAVIATE_API_HTTPS=true

# LIVEKIT
NEXT_PUBLIC_LIVEKIT_URL=wss://wizemeetings-girjhbt6.livekit.cloud
LIVEKIT_API_URL=wss://wizemeetings-girjhbt6.livekit.cloud
LIVEKIT_API_KEY=APIEEpA9sV4DQsQ
LIVEKIT_API_SECRET=V9nVi3NZAFmlaqSSH5J3GWaaWDGqB2YO8PJ3swie70S

# Auth
AUTH_GITHUB_ID=********************
AUTH_GITHUB_SECRET=****************************************
AUTH_GITHUB_SUPABASE_CALLBACK_URL=https://********************.supabase.co/auth/v1/callback

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# E2B
E2B_API_KEY=e2b_a4eacac3e614776baf827146361ca906f711598d

# LANG CHAIN
LANGCHAIN_API_KEY=***************************************************
LANGCHAIN_CALLBACKS_BACKGROUND=true

# OpenAI
# -- project: Arkitekt

# TAVILY
TAVILY_API_URL=https://api.tavily.com
TAVILY_API_KEY=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


# MapBox
NEXT_PUBLIC_MAPBOX_API_KEY=**********************************************************************************************
NEXT_PUBLIC_MAPBOX_PK=pk.eyJ1Ijoic2E5ZWRlc2lnbnMiLCJhIjoiY2tyc254ZjVxMzhibzJucnZ0bm0xem91ZSJ9.bBRyK-ANZ6NL3msEv2C1Hw
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1Ijoic2E5ZWRlc2lnbnMiLCJhIjoiY2tyc254ZjVxMzhibzJucnZ0bm0xem91ZSJ9.bBRyK-ANZ6NL3msEv2C1Hw
NEXT_PUBLIC_SUPERAGENT_API_URL=https://api.beta.superagent.sh

# LiveBlocks
LIVEBLOCKS_SECRET_KEY=sk_prod_TCO8hQavX7TZm2G45OThCNaih_JS3HzV_rr9PmSyJjeajBuYGJwHqXIfW6QNzP2V

# ElevenLabs
ELEVENLABS_API_KEY=***************************************************

# LIVEBLOCKS
NEXT_PUBLIC_LIVEBLOCKS_SECRET_KEY=sk_dev_lgXEwMpMigaVPjS4XtgMO55V54CHmAPUFp-Sd9pM4_pf1sg3xPeuZKvjvajaAfev
NEXT_PUBLIC_LIVEBLOCKS_PUBLIC_KEY=pk_dev_pAu06LllonEWbvOItPti4-Z67bmjmDpxPN6IoP6gFH8Tykdh8HKDimIH_wj4qJSZ

# DEEPGRAM
DEEPGRAM_API_KEY=****************************************
# Data Sources
# Jira
NEXT_PUBLIC_JIRA_TEST_AUTH_EMAIL=<EMAIL>
NEXT_PUBLIC_JIRA_TEST_AUTH_API_TOKEN=ATATT3xFfGF0nyEH71HXct8wncckZwl5NtsNCPuUSpjp7oy4-5YiK1ySgJBGzfwzcmeM7qZjWqbpxGySTJHPH2MFm0dYd71K1n9MY6Se5HpEXbKnzhMh7fzWP48UpjUb0wMI_ppzY1R3RWqCcXWzmTUz97N39PAFbSlSMcna086mbpeuL7-i0z8=7B5608EE,

#AUTH0
AUTH0_SECRET=QWLFKPPSP5S2JNXVLPB6THXZ

#WORKWIZE
NEXT_PUBLIC_WORKWIZE_CLIENT_ID=
NEXT_PUBLIC_WORKWIZE_CLIENT_SECRET=UDiyPOEzEjzhj3Ox7ZHTRJFhyPqxRav2ic4chYt0TL4=

#KAGGLE
NEXT_PUBLIC_KAGGLE_API_KEY=4916a683d838ccb4d5a33e4b28be279d
TRIGGER_API_KEY=tr_dev_wNsAg29TN6ZSsSp0z15N
TRIGGER_API_URL=https://trigger.weevolve-ai.com
NEXT_PUBLIC_TRIGGER_PUBLIC_API_KEY=pk_dev_DtFNII0UUBykcugjFLws

#BUNDLE ANALYZER
ANALYZE=false

# Disable mock auth
NEXT_PUBLIC_MOCK_AUTH_ENABLED=false

# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://appwrite.weevolve-ai.com/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=678f7ab80030f0f54fab
NEXT_PUBLIC_APPWRITE_BUCKET_ID=678f7ad200033658a79f
NEXT_PUBLIC_APPWRITE_PUBLIC_KEY=standard_9c7765160896f20e3b33db4a2a09a5a1aabc2d83ee4ecb773e6a9bbb9cbda66b51e45dc3f86e7ae870ee7cb296eab8ee97b8e872038c05eb4a4bb017a1e9d380a37a98bb882a091af760f095af751f90f7c94c4712972d14078d3402b676706ccc124a88e60464c3e2337a82411bb4eb7de5637afef1ed4f7760448880135f99

# COOLIFY
COOLIFY_TOKEN=2|iVdW3LP1ILenHxI4WYYw516BCIIaUbqGRWb1a8Veddcdc192

# CodeSandBox
CODESANDBOX_TOKEN=csb_v1_BT7TI4seBxixlo5Q7wiNzvY16kMBsYQ-Xj-rcZMtbK4

#PROXMOX
NEXT_PUBLIC_PROXMOX_URL=https://102.216.234.35:8006
#PROXMOX CLIENT API
NEXT_PUBLIC_PROXMOX_USERNAME=proxmox-client
NEXT_PUBLIC_PROXMOX_PASSWORD=T3chn0l0gy@proxmox-js
NEXT_PUBLIC_PROXMOX_TOKEN=21f68f9c-2e0d-4675-a20f-b61504fa6c
NEXT_PUBLIC_PROXMOX_TOKEN_ID=proxmox-client@pam!api-access
NEXT_PUBLIC_PROXMOX_REALM=pam

#PROXMOX ROOT API
NEXT_PUBLIC_PROXMOX_ROOT_USERNAME=root
NEXT_PUBLIC_PROXMOX_ROOT_PASSWORD=T3chn0l0gy@1
NEXT_PUBLIC_PROXMOX_ROOT_TOKEN=d3a03d93-d1e2-48ce-b764-c9c6ec11b67b
NEXT_PUBLIC_PROXMOX_ROOT_TOKEN_ID=root@pam!api-access
NEXT_PUBLIC_PROXMOX_ROOT_REALM=pam
