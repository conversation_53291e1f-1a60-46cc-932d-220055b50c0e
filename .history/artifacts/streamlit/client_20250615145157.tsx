import { Artifact } from '@/components/create-artifact';
import { StreamlitEditor } from '@/components/streamlit-editor';
import { PlayIcon, StopIcon, RefreshIcon, CopyIcon } from '@/components/icons';
import { toast } from 'sonner';
import { generateUUID } from '@/lib/utils';

interface StreamlitProject {
  files: Record<string, string>;
  entryPoint: string;
  title: string;
  description: string;
}

interface StreamlitProcess {
  id: string;
  projectId: string;
  port: number;
  status: 'starting' | 'running' | 'stopped' | 'error';
  url?: string;
  error?: string;
}

interface StreamlitArtifactMetadata {
  process?: StreamlitProcess;
  isRunning: boolean;
  logs: Array<{
    id: string;
    type: 'stdout' | 'stderr' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

const parseStreamlitProject = (content: string): StreamlitProject | null => {
  try {
    const parsed = JSON.parse(content);
    if (parsed.files && parsed.entryPoint && parsed.title && parsed.description) {
      return parsed as StreamlitProject;
    }
  } catch (error) {
    console.error('Failed to parse Streamlit project:', error);
  }
  return null;
};

const startStreamlitApp = async (projectId: string, project: StreamlitProject): Promise<StreamlitProcess> => {
  const response = await fetch('/api/streamlit?action=start', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, project }),
  });

  if (!response.ok) {
    throw new Error('Failed to start Streamlit app');
  }

  const data = await response.json();
  return data.process;
};

const stopStreamlitApp = async (projectId: string): Promise<void> => {
  const response = await fetch('/api/streamlit?action=stop', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId }),
  });

  if (!response.ok) {
    throw new Error('Failed to stop Streamlit app');
  }
};

const updateStreamlitApp = async (projectId: string, project: StreamlitProject): Promise<StreamlitProcess> => {
  const response = await fetch('/api/streamlit?action=update', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, project }),
  });

  if (!response.ok) {
    throw new Error('Failed to update Streamlit app');
  }

  const data = await response.json();
  return data.process;
};

export const streamlitArtifact = new Artifact<'streamlit', StreamlitArtifactMetadata>({
  kind: 'streamlit',
  description: 'Useful for creating interactive Streamlit web applications with real-time preview.',
  initialize: async ({ setMetadata }) => {
    setMetadata({
      isRunning: false,
      logs: [],
    });
  },
  onStreamPart: ({ streamPart, setArtifact }) => {
    if (streamPart.type === 'streamlit-delta') {
      setArtifact((draftArtifact) => ({
        ...draftArtifact,
        content: streamPart.content as string,
        isVisible:
          draftArtifact.status === 'streaming' &&
          draftArtifact.content.length > 100 &&
          draftArtifact.content.length < 200
            ? true
            : draftArtifact.isVisible,
        status: 'streaming',
      }));
    }
  },
  content: ({ metadata, setMetadata, ...props }) => {
    return (
      <StreamlitEditor
        {...props}
        metadata={metadata}
        setMetadata={setMetadata}
      />
    );
  },
  actions: [
    {
      icon: <PlayIcon size={18} />,
      label: 'Run',
      description: 'Start Streamlit application',
      onClick: async ({ content, metadata, setMetadata }) => {
        const project = parseStreamlitProject(content);
        if (!project) {
          toast.error('Invalid Streamlit project format');
          return;
        }

        const projectId = generateUUID();
        
        setMetadata((prev) => ({
          ...prev,
          isRunning: true,
          logs: [
            ...prev.logs,
            {
              id: generateUUID(),
              type: 'info',
              message: 'Starting Streamlit application...',
              timestamp: new Date(),
            },
          ],
        }));

        try {
          const process = await startStreamlitApp(projectId, project);
          
          setMetadata((prev) => ({
            ...prev,
            process,
            isRunning: process.status === 'running' || process.status === 'starting',
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'info',
                message: `Streamlit app started on port ${process.port}`,
                timestamp: new Date(),
              },
            ],
          }));

          toast.success('Streamlit app started successfully');
        } catch (error) {
          setMetadata((prev) => ({
            ...prev,
            isRunning: false,
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'stderr',
                message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date(),
              },
            ],
          }));
          toast.error('Failed to start Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => metadata?.isRunning || false,
    },
    {
      icon: <StopIcon size={18} />,
      label: 'Stop',
      description: 'Stop Streamlit application',
      onClick: async ({ metadata, setMetadata }) => {
        if (!metadata?.process?.projectId) {
          toast.error('No running Streamlit app to stop');
          return;
        }

        try {
          await stopStreamlitApp(metadata.process.projectId);
          
          setMetadata((prev) => ({
            ...prev,
            process: undefined,
            isRunning: false,
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'info',
                message: 'Streamlit app stopped',
                timestamp: new Date(),
              },
            ],
          }));

          toast.success('Streamlit app stopped');
        } catch (error) {
          toast.error('Failed to stop Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => !metadata?.isRunning,
    },
    {
      icon: <RefreshIcon size={18} />,
      label: 'Restart',
      description: 'Restart Streamlit application with current code',
      onClick: async ({ content, metadata, setMetadata }) => {
        const project = parseStreamlitProject(content);
        if (!project || !metadata?.process?.projectId) {
          toast.error('Cannot restart: invalid project or no running app');
          return;
        }

        setMetadata((prev) => ({
          ...prev,
          logs: [
            ...prev.logs,
            {
              id: generateUUID(),
              type: 'info',
              message: 'Restarting Streamlit application...',
              timestamp: new Date(),
            },
          ],
        }));

        try {
          const process = await updateStreamlitApp(metadata.process.projectId, project);
          
          setMetadata((prev) => ({
            ...prev,
            process,
            isRunning: process.status === 'running' || process.status === 'starting',
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'info',
                message: 'Streamlit app restarted successfully',
                timestamp: new Date(),
              },
            ],
          }));

          toast.success('Streamlit app restarted');
        } catch (error) {
          toast.error('Failed to restart Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => !metadata?.process,
    },
    {
      icon: <CopyIcon size={18} />,
      label: 'Copy URL',
      description: 'Copy Streamlit app URL to clipboard',
      onClick: async ({ metadata }) => {
        if (!metadata?.process?.url) {
          toast.error('No running Streamlit app URL to copy');
          return;
        }

        try {
          await navigator.clipboard.writeText(metadata.process.url);
          toast.success('URL copied to clipboard');
        } catch (error) {
          toast.error('Failed to copy URL');
        }
      },
      isDisabled: ({ metadata }) => !metadata?.process?.url,
    },
  ],
  toolbar: [],
});
