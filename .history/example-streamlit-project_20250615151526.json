{"files": {"app.py": "import streamlit as st\nimport pandas as pd\nimport numpy as np\nimport matplotlib.pyplot as plt\n\n# Set page config\nst.set_page_config(\n    page_title=\"Data Dashboard\",\n    page_icon=\"📊\",\n    layout=\"wide\"\n)\n\n# Title\nst.title(\"📊 Interactive Data Dashboard\")\nst.markdown(\"Welcome to your browser-based Streamlit dashboard powered by Pyodide!\")\n\n# Sidebar\nst.sidebar.header(\"Controls\")\n\n# Generate sample data\nnp.random.seed(42)\nn_points = st.sidebar.slider(\"Number of data points\", 50, 500, 200)\n\n# Create sample dataset\ndata = {\n    'x': np.random.randn(n_points),\n    'y': np.random.randn(n_points),\n    'category': np.random.choice(['A', 'B', 'C'], n_points),\n    'value': np.random.randint(1, 100, n_points)\n}\ndf = pd.DataFrame(data)\n\n# Main content\ncol1, col2 = st.columns(2)\n\nwith col1:\n    st.subheader(\"Scatter Plot\")\n    fig, ax = plt.subplots(figsize=(8, 6))\n    \n    # Create scatter plot with different colors for categories\n    for category in df['category'].unique():\n        mask = df['category'] == category\n        ax.scatter(df[mask]['x'], df[mask]['y'], \n                  s=df[mask]['value'], alpha=0.6, label=category)\n    \n    ax.set_xlabel('X values')\n    ax.set_ylabel('Y values')\n    ax.set_title('Interactive Scatter Plot')\n    ax.legend()\n    ax.grid(True, alpha=0.3)\n    \n    st.pyplot(fig)\n\nwith col2:\n    st.subheader(\"Distribution\")\n    selected_category = st.selectbox(\"Select category\", ['All'] + list(df['category'].unique()))\n    \n    if selected_category == 'All':\n        filtered_df = df\n    else:\n        filtered_df = df[df['category'] == selected_category]\n    \n    fig, ax = plt.subplots(figsize=(8, 6))\n    ax.hist(filtered_df['value'], bins=20, alpha=0.7, edgecolor='black')\n    ax.set_xlabel('Value')\n    ax.set_ylabel('Frequency')\n    ax.set_title(f'Value Distribution - {selected_category}')\n    ax.grid(True, alpha=0.3)\n    \n    st.pyplot(fig)\n\n# Data table\nst.subheader(\"Data Table\")\nif st.checkbox(\"Show raw data\"):\n    st.dataframe(df, use_container_width=True)\n\n# Metrics\nst.subheader(\"Summary Statistics\")\ncol1, col2, col3, col4 = st.columns(4)\n\nwith col1:\n    st.metric(\"Total Points\", len(df))\nwith col2:\n    st.metric(\"Average Value\", f\"{df['value'].mean():.1f}\")\nwith col3:\n    st.metric(\"Max Value\", df['value'].max())\nwith col4:\n    st.metric(\"Categories\", df['category'].nunique())\n\n# Info about browser execution\nst.info(\"🚀 This Streamlit app is running entirely in your browser using Pyodide and WebAssembly!\")", "requirements.txt": "streamlit\npandas\nnumpy\nmat<PERSON><PERSON><PERSON>b", "README.md": "# Interactive Data Dashboard\n\nA browser-based Streamlit application powered by Pyodide and WebAssembly.\n\n## Features\n\n- Interactive scatter plot with category colors and value-based sizing\n- Filterable histogram by category\n- Dynamic data table display\n- Summary statistics metrics\n- Responsive layout with sidebar controls\n- **Runs entirely in the browser** - no server required!\n\n## Technology\n\n- **Streamlit**: Web app framework\n- **Pyodide**: Python runtime in the browser\n- **WebAssembly**: High-performance execution\n- **stlite**: Browser port of Streamlit\n\n## Usage\n\n1. Use the sidebar slider to adjust the number of data points\n2. Select different categories in the distribution chart\n3. Toggle the raw data table view\n4. Explore the interactive plots\n\n## Browser Compatibility\n\nThis app runs in modern browsers that support WebAssembly:\n- Chrome 57+\n- Firefox 52+\n- Safari 11+\n- Edge 16+"}, "entryPoint": "app.py", "title": "Browser-Based Data Dashboard", "description": "An interactive Streamlit dashboard running entirely in the browser using Pyodide and WebAssembly."}