// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id       String     @id @default(uuid())
  email    String     @unique
  password String?
  chats    Chat[]
  documents Document[]
  suggestions Suggestion[]
}

model Chat {
  id         String     @id @default(uuid())
  createdAt  DateTime
  title      String
  userId     String
  visibility String     @default("private")
  user       User       @relation(fields: [userId], references: [id])
  messages   Message[]
  votes      Vote[]
  streams    Stream[]
}

model Message {
  id         String   @id @default(uuid())
  chatId     String
  role       String
  parts      Json
  attachments J<PERSON>
  createdAt  DateTime
  chat       Chat     @relation(fields: [chatId], references: [id])
  votes      Vote[]
}

model Vote {
  chatId     String
  messageId  String
  isUpvoted  Boolean
  chat       Chat     @relation(fields: [chatId], references: [id])
  message    Message  @relation(fields: [messageId], references: [id])

  @@id([chatId, messageId])
}

model Document {
  id          String       @id @default(uuid())
  createdAt   DateTime
  title       String
  content     String?
  kind        String       @default("text")
  userId      String
  user        User         @relation(fields: [userId], references: [id])
  suggestions Suggestion[]

  @@unique([id, createdAt])
}

model Suggestion {
  id                String   @id @default(uuid())
  documentId        String
  documentCreatedAt DateTime
  originalText      String
  suggestedText     String
  description       String?
  isResolved        Boolean  @default(false)
  userId            String
  createdAt         DateTime
  user              User     @relation(fields: [userId], references: [id])
  document          Document @relation(fields: [documentId, documentCreatedAt], references: [id, createdAt])
}

model Stream {
  id        String   @id @default(uuid())
  chatId    String
  createdAt DateTime
  chat      Chat     @relation(fields: [chatId], references: [id])
}
