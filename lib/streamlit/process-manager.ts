import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { nanoid } from 'nanoid';

export interface StreamlitProject {
  files: Record<string, string>;
  entryPoint: string;
  title: string;
  description: string;
}

export interface StreamlitProcess {
  id: string;
  projectId: string;
  process: ChildProcess;
  port: number;
  status: 'starting' | 'running' | 'stopped' | 'error';
  projectPath: string;
  url?: string;
  error?: string;
}

export interface ProcessOutput {
  type: 'stdout' | 'stderr';
  data: string;
  timestamp: Date;
}

class StreamlitProcessManager {
  private processes = new Map<string, StreamlitProcess>();
  private projectPaths = new Map<string, string>();
  private readonly basePort = 8501;
  private readonly maxProcesses = 10;

  /**
   * Create project files and start Streamlit process
   */
  async startStreamlitApp(
    projectId: string,
    project: StreamlitProject,
    onOutput?: (output: ProcessOutput) => void
  ): Promise<StreamlitProcess> {
    // Stop existing process if running
    await this.stopProcess(projectId);

    // Create project directory
    const projectPath = await this.createProjectFiles(projectId, project);
    
    // Find available port
    const port = await this.findAvailablePort();
    
    // Create process entry
    const processId = nanoid();
    const streamlitProcess: StreamlitProcess = {
      id: processId,
      projectId,
      process: null as any, // Will be set below
      port,
      status: 'starting',
      projectPath,
    };

    try {
      // Start Streamlit process
      const process = spawn('streamlit', [
        'run',
        project.entryPoint,
        '--server.port',
        port.toString(),
        '--server.headless',
        'true',
        '--server.enableCORS',
        'false',
        '--server.enableXsrfProtection',
        'false',
        '--browser.gatherUsageStats',
        'false',
      ], {
        cwd: projectPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONPATH: projectPath,
        },
      });

      streamlitProcess.process = process;
      streamlitProcess.url = `http://localhost:${port}`;

      // Handle process output
      process.stdout?.on('data', (data) => {
        const output: ProcessOutput = {
          type: 'stdout',
          data: data.toString(),
          timestamp: new Date(),
        };
        onOutput?.(output);

        // Check if Streamlit is ready
        if (data.toString().includes('You can now view your Streamlit app')) {
          streamlitProcess.status = 'running';
        }
      });

      process.stderr?.on('data', (data) => {
        const output: ProcessOutput = {
          type: 'stderr',
          data: data.toString(),
          timestamp: new Date(),
        };
        onOutput?.(output);
      });

      process.on('exit', (code) => {
        if (code !== 0) {
          streamlitProcess.status = 'error';
          streamlitProcess.error = `Process exited with code ${code}`;
        } else {
          streamlitProcess.status = 'stopped';
        }
        this.processes.delete(projectId);
      });

      process.on('error', (error) => {
        streamlitProcess.status = 'error';
        streamlitProcess.error = error.message;
        onOutput?.({
          type: 'stderr',
          data: `Process error: ${error.message}`,
          timestamp: new Date(),
        });
      });

      this.processes.set(projectId, streamlitProcess);
      return streamlitProcess;

    } catch (error) {
      streamlitProcess.status = 'error';
      streamlitProcess.error = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    }
  }

  /**
   * Stop a running Streamlit process
   */
  async stopProcess(projectId: string): Promise<void> {
    const streamlitProcess = this.processes.get(projectId);
    if (streamlitProcess?.process) {
      streamlitProcess.process.kill('SIGTERM');
      streamlitProcess.status = 'stopped';
      this.processes.delete(projectId);
    }
  }

  /**
   * Get process status
   */
  getProcess(projectId: string): StreamlitProcess | undefined {
    return this.processes.get(projectId);
  }

  /**
   * Update project files and restart if needed
   */
  async updateProject(
    projectId: string,
    project: StreamlitProject,
    onOutput?: (output: ProcessOutput) => void
  ): Promise<StreamlitProcess> {
    // Update files
    await this.createProjectFiles(projectId, project);
    
    // Restart process if it was running
    const existingProcess = this.processes.get(projectId);
    if (existingProcess && existingProcess.status === 'running') {
      return this.startStreamlitApp(projectId, project, onOutput);
    }
    
    return existingProcess || await this.startStreamlitApp(projectId, project, onOutput);
  }

  /**
   * Create project files on disk
   */
  private async createProjectFiles(projectId: string, project: StreamlitProject): Promise<string> {
    const projectPath = path.join(process.cwd(), 'tmp', 'streamlit', projectId);
    
    // Create directory
    await fs.mkdir(projectPath, { recursive: true });
    
    // Write all files
    for (const [filename, content] of Object.entries(project.files)) {
      const filePath = path.join(projectPath, filename);
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, content, 'utf-8');
    }

    this.projectPaths.set(projectId, projectPath);
    return projectPath;
  }

  /**
   * Find an available port for Streamlit
   */
  private async findAvailablePort(): Promise<number> {
    const usedPorts = new Set(
      Array.from(this.processes.values()).map(p => p.port)
    );

    for (let port = this.basePort; port < this.basePort + 100; port++) {
      if (!usedPorts.has(port)) {
        return port;
      }
    }

    throw new Error('No available ports for Streamlit');
  }

  /**
   * Clean up all processes and temporary files
   */
  async cleanup(): Promise<void> {
    // Stop all processes
    for (const [projectId] of this.processes) {
      await this.stopProcess(projectId);
    }

    // Clean up temporary files
    try {
      const tmpDir = path.join(process.cwd(), 'tmp', 'streamlit');
      await fs.rm(tmpDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }
  }

  /**
   * Get all running processes
   */
  getAllProcesses(): StreamlitProcess[] {
    return Array.from(this.processes.values());
  }
}

// Export singleton instance
export const streamlitProcessManager = new StreamlitProcessManager();

// Cleanup on process exit
process.on('exit', () => {
  streamlitProcessManager.cleanup();
});

process.on('SIGINT', () => {
  streamlitProcessManager.cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  streamlitProcessManager.cleanup();
  process.exit(0);
});
