// Export all schemas
export * from './schema';

// Re-export database managers
export { DbAgentManager } from './db-agent-manager';
export { DbMicroVMManager } from './db-microvm-manager';

// Connect to the database
import { drizzle } from 'drizzle-orm/vercel-postgres';
import { sql } from '@vercel/postgres';
import * as schema from './schema';

// Create the database client
export const db = drizzle(sql, { schema }); 