import { nanoid } from 'nanoid';
import { db } from '@/lib/db';
import { agents } from './schema';
import { Agent, AgentCreateOptions, AgentManager } from '../types';
import { eq } from 'drizzle-orm';

export class DbAgentManager implements AgentManager {
  async createAgent(options: AgentCreateOptions): Promise<Agent> {
    const id = nanoid();
    
    const agentData = {
      id,
      name: options.name,
      role: options.role,
      description: options.description,
      systemPrompt: options.systemPrompt,
      tools: options.tools || [],
      microVMId: options.microVMId,
      userId: 'system', // In a real app, this would come from authentication
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    await db.insert(agents).values(agentData);
    
    // Return the agent we just created
    return this.getAgent(id) as Promise<Agent>;
  }

  async getAgent(id: string): Promise<Agent | null> {
    const result = await db
      .select()
      .from(agents)
      .where(eq(agents.id, id));
    
    if (result.length === 0) {
      return null;
    }
    
    const agent = result[0];
    
    // Transform the database row to Agent interface
    return {
      id: agent.id,
      name: agent.name,
      role: agent.role as any, // Cast to AgentRole
      description: agent.description,
      systemPrompt: agent.systemPrompt,
      tools: agent.tools as string[],
      microVMId: agent.microVMId || undefined,
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      userId: agent.userId,
    };
  }

  async listAgents(userId: string): Promise<Agent[]> {
    const result = await db
      .select()
      .from(agents)
      .where(eq(agents.userId, userId));
    
    // Transform database rows to Agent interface
    return result.map(agent => ({
      id: agent.id,
      name: agent.name,
      role: agent.role as any, // Cast to AgentRole
      description: agent.description,
      systemPrompt: agent.systemPrompt,
      tools: agent.tools as string[],
      microVMId: agent.microVMId || undefined,
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      userId: agent.userId,
    }));
  }

  async updateAgent(id: string, updates: Partial<AgentCreateOptions>): Promise<Agent> {
    const agent = await this.getAgent(id);
    if (!agent) {
      throw new Error(`Agent with id ${id} not found`);
    }
    
    // Create update object with only the fields that were provided
    const updateData: Record<string, any> = {
      updatedAt: new Date()
    };
    
    if (updates.name !== undefined) {
      updateData.name = updates.name;
    }
    
    if (updates.role !== undefined) {
      updateData.role = updates.role;
    }
    
    if (updates.description !== undefined) {
      updateData.description = updates.description;
    }
    
    if (updates.systemPrompt !== undefined) {
      updateData.systemPrompt = updates.systemPrompt;
    }
    
    if (updates.tools !== undefined) {
      updateData.tools = updates.tools;
    }
    
    if (updates.microVMId !== undefined) {
      updateData.microVMId = updates.microVMId;
    }
    
    await db
      .update(agents)
      .set(updateData)
      .where(eq(agents.id, id));
    
    // Return the updated agent
    return this.getAgent(id) as Promise<Agent>;
  }

  async deleteAgent(id: string): Promise<void> {
    const agent = await this.getAgent(id);
    if (!agent) {
      throw new Error(`Agent with id ${id} not found`);
    }
    
    await db
      .delete(agents)
      .where(eq(agents.id, id));
  }

  async assignVMToAgent(agentId: string, microVMId: string): Promise<Agent> {
    const agent = await this.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent with id ${agentId} not found`);
    }
    
    await db
      .update(agents)
      .set({
        microVMId,
        updatedAt: new Date()
      })
      .where(eq(agents.id, agentId));
    
    // Return the updated agent
    return this.getAgent(agentId) as Promise<Agent>;
  }
} 