import { pgTable, serial, text, timestamp, integer, jsonb, foreignKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

export const microVms = pgTable('micro_vms', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  status: text('status').notNull().default('creating'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  memory: integer('memory').notNull().default(512),
  cpu: integer('cpu').notNull().default(1),
  storage: integer('storage').notNull().default(5),
  image: text('image').notNull(),
  ports: jsonb('ports').notNull().$type<number[]>().default([]),
  envVars: jsonb('env_vars').notNull().$type<Record<string, string>>().default({}),
  userId: text('user_id').notNull(),
});

export const microVmsRelations = relations(microVms, ({ one }) => ({
  agent: one(agents, {
    fields: [microVms.id],
    references: [agents.microVMId],
  }),
}));

export const agents = pgTable('agents', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  role: text('role').notNull(),
  description: text('description').notNull(),
  systemPrompt: text('system_prompt').notNull(),
  tools: jsonb('tools').notNull().$type<string[]>().default([]),
  microVMId: text('micro_vm_id').references(() => microVms.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  userId: text('user_id').notNull(),
});

export const agentsRelations = relations(agents, ({ one }) => ({
  microVM: one(microVms, {
    fields: [agents.microVMId],
    references: [microVms.id],
  }),
}));

export const vmCommands = pgTable('vm_commands', {
  id: serial('id').primaryKey(),
  agentId: text('agent_id').notNull().references(() => agents.id, { onDelete: 'cascade' }),
  command: text('command').notNull(),
  stdout: text('stdout'),
  stderr: text('stderr'),
  exitCode: integer('exit_code'),
  executedAt: timestamp('executed_at').defaultNow().notNull(),
  userId: text('user_id').notNull(),
});

export const vmCommandsRelations = relations(vmCommands, ({ one }) => ({
  agent: one(agents, {
    fields: [vmCommands.agentId],
    references: [agents.id],
  }),
}));

export type AgentRow = typeof agents.$inferSelect;
export type AgentInsert = typeof agents.$inferInsert;

export type MicroVMRow = typeof microVms.$inferSelect;
export type MicroVMInsert = typeof microVms.$inferInsert;

export type VMCommandRow = typeof vmCommands.$inferSelect;
export type VMCommandInsert = typeof vmCommands.$inferInsert; 