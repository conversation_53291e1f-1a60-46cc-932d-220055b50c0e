import { sql } from '@vercel/postgres';
import { drizzle } from 'drizzle-orm/vercel-postgres';
import { migrate } from 'drizzle-orm/vercel-postgres/migrator';
import * as schema from './schema';

// Database client
const db = drizzle(sql, { schema });

// Create tables if they don't exist
async function createTables() {
  console.log('Running migrations...');
  
  try {
    // Create microVMs table
    await sql`
      CREATE TABLE IF NOT EXISTS "micro_vms" (
        "id" TEXT PRIMARY KEY,
        "name" TEXT NOT NULL,
        "status" TEXT NOT NULL DEFAULT 'creating',
        "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
        "memory" INTEGER NOT NULL DEFAULT 512,
        "cpu" INTEGER NOT NULL DEFAULT 1,
        "storage" INTEGER NOT NULL DEFAULT 5,
        "image" TEXT NOT NULL,
        "ports" JSONB NOT NULL DEFAULT '[]',
        "env_vars" JSONB NOT NULL DEFAULT '{}',
        "user_id" TEXT NOT NULL
      );
    `;
    
    // Create agents table
    await sql`
      CREATE TABLE IF NOT EXISTS "agents" (
        "id" TEXT PRIMARY KEY,
        "name" TEXT NOT NULL,
        "role" TEXT NOT NULL,
        "description" TEXT NOT NULL,
        "system_prompt" TEXT NOT NULL,
        "tools" JSONB NOT NULL DEFAULT '[]',
        "micro_vm_id" TEXT REFERENCES "micro_vms"("id") ON DELETE SET NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
        "user_id" TEXT NOT NULL
      );
    `;
    
    // Create vmCommands table
    await sql`
      CREATE TABLE IF NOT EXISTS "vm_commands" (
        "id" SERIAL PRIMARY KEY,
        "agent_id" TEXT NOT NULL REFERENCES "agents"("id") ON DELETE CASCADE,
        "command" TEXT NOT NULL,
        "stdout" TEXT,
        "stderr" TEXT,
        "exit_code" INTEGER,
        "executed_at" TIMESTAMP NOT NULL DEFAULT NOW(),
        "user_id" TEXT NOT NULL
      );
    `;
    
    console.log('Migrations completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    await createTables();
    process.exit(0);
  } catch (error) {
    console.error('Failed to set up database:', error);
    process.exit(1);
  }
}

// Run migrations when executed directly
if (require.main === module) {
  main();
}

// Export for use in other files
export { createTables }; 