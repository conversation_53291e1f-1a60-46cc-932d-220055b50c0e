import { drizzle } from 'drizzle-orm/vercel-postgres';
import { sql } from '@vercel/postgres';

// Core app schemas
import * as authSchema from './schema';

// MicroVM schemas
import * as microVMSchema from '../microvm/db/schema';

// Merge all schemas
const schema = {
  ...authSchema,
  ...microVMSchema,
};

// Create the database client
export const db = drizzle(sql);

// Export from the schema
export * from './schema'; 