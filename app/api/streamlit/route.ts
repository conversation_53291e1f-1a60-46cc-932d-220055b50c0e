import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { streamlitProcessManager, StreamlitProject } from '@/lib/streamlit/process-manager';
import { z } from 'zod';

const streamlitProjectSchema = z.object({
  files: z.record(z.string()),
  entryPoint: z.string(),
  title: z.string(),
  description: z.string(),
});

const startRequestSchema = z.object({
  projectId: z.string(),
  project: streamlitProjectSchema,
});

const stopRequestSchema = z.object({
  projectId: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'start') {
      const body = await request.json();
      const { projectId, project } = startRequestSchema.parse(body);

      // Add user ID to project ID for isolation
      const userProjectId = `${session.user.id}-${projectId}`;

      const streamlitProcess = await streamlitProcessManager.startStreamlitApp(
        userProjectId,
        project,
        (output) => {
          // In a real implementation, you might want to stream this via WebSocket
          console.log(`[${userProjectId}] ${output.type}: ${output.data}`);
        }
      );

      return NextResponse.json({
        success: true,
        process: {
          id: streamlitProcess.id,
          projectId: streamlitProcess.projectId,
          port: streamlitProcess.port,
          status: streamlitProcess.status,
          url: streamlitProcess.url,
        },
      });
    }

    if (action === 'stop') {
      const body = await request.json();
      const { projectId } = stopRequestSchema.parse(body);

      const userProjectId = `${session.user.id}-${projectId}`;
      await streamlitProcessManager.stopProcess(userProjectId);

      return NextResponse.json({ success: true });
    }

    if (action === 'status') {
      const projectId = searchParams.get('projectId');
      if (!projectId) {
        return NextResponse.json({ error: 'Project ID required' }, { status: 400 });
      }

      const userProjectId = `${session.user.id}-${projectId}`;
      const process = streamlitProcessManager.getProcess(userProjectId);

      if (!process) {
        return NextResponse.json({ error: 'Process not found' }, { status: 404 });
      }

      return NextResponse.json({
        process: {
          id: process.id,
          projectId: process.projectId,
          port: process.port,
          status: process.status,
          url: process.url,
          error: process.error,
        },
      });
    }

    if (action === 'update') {
      const body = await request.json();
      const { projectId, project } = startRequestSchema.parse(body);

      const userProjectId = `${session.user.id}-${projectId}`;

      const streamlitProcess = await streamlitProcessManager.updateProject(
        userProjectId,
        project,
        (output) => {
          console.log(`[${userProjectId}] ${output.type}: ${output.data}`);
        }
      );

      return NextResponse.json({
        success: true,
        process: {
          id: streamlitProcess.id,
          projectId: streamlitProcess.projectId,
          port: streamlitProcess.port,
          status: streamlitProcess.status,
          url: streamlitProcess.url,
        },
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Streamlit API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (projectId) {
      const userProjectId = `${session.user.id}-${projectId}`;
      const process = streamlitProcessManager.getProcess(userProjectId);

      if (!process) {
        return NextResponse.json({ error: 'Process not found' }, { status: 404 });
      }

      return NextResponse.json({
        process: {
          id: process.id,
          projectId: process.projectId,
          port: process.port,
          status: process.status,
          url: process.url,
          error: process.error,
        },
      });
    }

    // Return all processes for the user
    const allProcesses = streamlitProcessManager.getAllProcesses();
    const userProcesses = allProcesses.filter(p => 
      p.projectId.startsWith(`${session.user.id}-`)
    );

    return NextResponse.json({
      processes: userProcesses.map(p => ({
        id: p.id,
        projectId: p.projectId,
        port: p.port,
        status: p.status,
        url: p.url,
        error: p.error,
      })),
    });

  } catch (error) {
    console.error('Streamlit API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
