import { z } from 'zod';
import { streamObject } from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { createDocumentHandler } from '@/lib/artifacts/server';
import { updateDocumentPrompt } from '@/lib/ai/prompts';

const streamlitPrompt = `
You are a Streamlit application generator that creates complete, functional Streamlit apps. When creating Streamlit applications:

1. Create a complete, runnable Streamlit application
2. Use proper Streamlit components and layout
3. Include meaningful interactivity (widgets, charts, etc.)
4. Add helpful comments explaining the code
5. Use appropriate Streamlit styling and formatting
6. Handle user inputs and display outputs effectively
7. Include error handling where appropriate
8. Use common Python libraries that work well with Streamlit (pandas, numpy, matplotlib, plotly, etc.)
9. Structure the code with proper organization
10. Add a clear title and description for the app

The response should be a JSON object with the following structure:
{
  "files": {
    "app.py": "# Main Streamlit application code",
    "requirements.txt": "streamlit\npandas\nnumpy\n...",
    "README.md": "# App Description\n..."
  },
  "entryPoint": "app.py",
  "title": "App Title",
  "description": "Brief description of what the app does"
}

Examples of good Streamlit apps:
- Data visualization dashboards
- Interactive calculators
- Data analysis tools
- Machine learning model demos
- Form-based applications
- Real-time data displays

Always include a requirements.txt file with necessary dependencies.
`;

const streamlitUpdatePrompt = (currentContent: string) => `
Update the following Streamlit application based on the given prompt. Maintain the existing structure and only modify what's necessary.

Current application:
${currentContent}

Return the updated application in the same JSON format with files, entryPoint, title, and description.
`;

export const streamlitDocumentHandler = createDocumentHandler<'streamlit'>({
  kind: 'streamlit',
  onCreateDocument: async ({ title, dataStream }) => {
    let draftContent = '';

    const { fullStream } = streamObject({
      model: myProvider.languageModel('artifact-model'),
      system: streamlitPrompt,
      prompt: title,
      schema: z.object({
        files: z.record(z.string()).describe('Object containing file paths as keys and file contents as values'),
        entryPoint: z.string().describe('Main file to run (usually app.py)'),
        title: z.string().describe('Application title'),
        description: z.string().describe('Brief description of the application'),
      }),
    });

    for await (const delta of fullStream) {
      const { type } = delta;

      if (type === 'object') {
        const { object } = delta;
        
        if (object.files || object.entryPoint || object.title || object.description) {
          const streamlitProject = {
            files: object.files || {},
            entryPoint: object.entryPoint || 'app.py',
            title: object.title || 'Streamlit App',
            description: object.description || 'A Streamlit application',
          };

          draftContent = JSON.stringify(streamlitProject, null, 2);

          dataStream.writeData({
            type: 'streamlit-delta',
            content: draftContent,
          });
        }
      }
    }

    return draftContent;
  },
  onUpdateDocument: async ({ document, description, dataStream }) => {
    let draftContent = '';

    const { fullStream } = streamObject({
      model: myProvider.languageModel('artifact-model'),
      system: streamlitUpdatePrompt(document.content || ''),
      prompt: description,
      schema: z.object({
        files: z.record(z.string()).describe('Object containing file paths as keys and file contents as values'),
        entryPoint: z.string().describe('Main file to run (usually app.py)'),
        title: z.string().describe('Application title'),
        description: z.string().describe('Brief description of the application'),
      }),
      experimental_providerMetadata: {
        openai: {
          prediction: {
            type: 'content',
            content: document.content,
          },
        },
      },
    });

    for await (const delta of fullStream) {
      const { type } = delta;

      if (type === 'object') {
        const { object } = delta;
        
        if (object.files || object.entryPoint || object.title || object.description) {
          const streamlitProject = {
            files: object.files || {},
            entryPoint: object.entryPoint || 'app.py',
            title: object.title || 'Streamlit App',
            description: object.description || 'A Streamlit application',
          };

          draftContent = JSON.stringify(streamlitProject, null, 2);

          dataStream.writeData({
            type: 'streamlit-delta',
            content: draftContent,
          });
        }
      }
    }

    return draftContent;
  },
});
