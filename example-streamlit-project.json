{"files": {"app.py": "import streamlit as st\nimport pandas as pd\nimport numpy as np\nimport plotly.express as px\n\n# Set page config\nst.set_page_config(\n    page_title=\"Data Dashboard\",\n    page_icon=\"📊\",\n    layout=\"wide\"\n)\n\n# Title\nst.title(\"📊 Interactive Data Dashboard\")\nst.markdown(\"Welcome to your interactive data visualization dashboard!\")\n\n# Sidebar\nst.sidebar.header(\"Controls\")\n\n# Generate sample data\nnp.random.seed(42)\nn_points = st.sidebar.slider(\"Number of data points\", 50, 500, 200)\n\n# Create sample dataset\ndata = {\n    'x': np.random.randn(n_points),\n    'y': np.random.randn(n_points),\n    'category': np.random.choice(['A', 'B', 'C'], n_points),\n    'value': np.random.randint(1, 100, n_points)\n}\ndf = pd.DataFrame(data)\n\n# Main content\ncol1, col2 = st.columns(2)\n\nwith col1:\n    st.subheader(\"Scatter Plot\")\n    fig_scatter = px.scatter(\n        df, \n        x='x', \n        y='y', \n        color='category',\n        size='value',\n        title=\"Interactive Scatter Plot\"\n    )\n    st.plotly_chart(fig_scatter, use_container_width=True)\n\nwith col2:\n    st.subheader(\"Distribution\")\n    selected_category = st.selectbox(\"Select category\", ['All'] + list(df['category'].unique()))\n    \n    if selected_category == 'All':\n        filtered_df = df\n    else:\n        filtered_df = df[df['category'] == selected_category]\n    \n    fig_hist = px.histogram(\n        filtered_df, \n        x='value', \n        title=f\"Value Distribution - {selected_category}\"\n    )\n    st.plotly_chart(fig_hist, use_container_width=True)\n\n# Data table\nst.subheader(\"Data Table\")\nif st.checkbox(\"Show raw data\"):\n    st.dataframe(df, use_container_width=True)\n\n# Metrics\nst.subheader(\"Summary Statistics\")\ncol1, col2, col3, col4 = st.columns(4)\n\nwith col1:\n    st.metric(\"Total Points\", len(df))\nwith col2:\n    st.metric(\"Average Value\", f\"{df['value'].mean():.1f}\")\nwith col3:\n    st.metric(\"Max Value\", df['value'].max())\nwith col4:\n    st.metric(\"Categories\", df['category'].nunique())", "requirements.txt": "streamlit>=1.28.0\npandas>=1.5.0\nnumpy>=1.24.0\nplotly>=5.15.0", "README.md": "# Interactive Data Dashboard\n\nA simple Streamlit application that demonstrates interactive data visualization.\n\n## Features\n\n- Interactive scatter plot with category colors and value-based sizing\n- Filterable histogram by category\n- Dynamic data table display\n- Summary statistics metrics\n- Responsive layout with sidebar controls\n\n## Usage\n\n1. Use the sidebar slider to adjust the number of data points\n2. Select different categories in the distribution chart\n3. Toggle the raw data table view\n4. Explore the interactive plots\n\n## Requirements\n\nSee `requirements.txt` for the required Python packages.\n\n## Running the App\n\n```bash\nstreamlit run app.py\n```\n\nThe app will be available at `http://localhost:8501`."}, "entryPoint": "app.py", "title": "Interactive Data Dashboard", "description": "A comprehensive Streamlit dashboard featuring interactive charts, data filtering, and real-time visualization controls."}