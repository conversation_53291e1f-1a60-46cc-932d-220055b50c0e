'use client';

import { useState, useEffect, useMemo } from 'react';
import { CodeEditor } from '@/components/code-editor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { FileIcon, PlayIcon, StopIcon, ExternalLinkIcon } from '@/components/icons';
import { cn } from '@/lib/utils';

interface StreamlitProject {
  files: Record<string, string>;
  entryPoint: string;
  title: string;
  description: string;
}

interface StreamlitProcess {
  id: string;
  projectId: string;
  port: number;
  status: 'starting' | 'running' | 'stopped' | 'error';
  url?: string;
  error?: string;
}

interface LogEntry {
  id: string;
  type: 'stdout' | 'stderr' | 'info';
  message: string;
  timestamp: Date;
}

interface StreamlitArtifactMetadata {
  process?: StreamlitProcess;
  isRunning: boolean;
  logs: LogEntry[];
}

interface StreamlitEditorProps {
  content: string;
  currentVersionIndex: number;
  isCurrentVersion: boolean;
  onSaveContent: (updatedContent: string, debounce: boolean) => void;
  status: 'streaming' | 'idle';
  metadata?: StreamlitArtifactMetadata;
  setMetadata?: (updater: (prev: StreamlitArtifactMetadata) => StreamlitArtifactMetadata) => void;
}

export function StreamlitEditor({
  content,
  currentVersionIndex,
  isCurrentVersion,
  onSaveContent,
  status,
  metadata,
  setMetadata,
}: StreamlitEditorProps) {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [showPreview, setShowPreview] = useState(true);

  const project = useMemo((): StreamlitProject | null => {
    try {
      const parsed = JSON.parse(content);
      if (parsed.files && parsed.entryPoint && parsed.title && parsed.description) {
        return parsed as StreamlitProject;
      }
    } catch (error) {
      console.error('Failed to parse Streamlit project:', error);
    }
    return null;
  }, [content]);

  // Set initial selected file
  useEffect(() => {
    if (project && !selectedFile) {
      setSelectedFile(project.entryPoint);
    }
  }, [project, selectedFile]);

  const handleFileContentChange = (newContent: string) => {
    if (!project || !selectedFile) return;

    const updatedProject = {
      ...project,
      files: {
        ...project.files,
        [selectedFile]: newContent,
      },
    };

    onSaveContent(JSON.stringify(updatedProject, null, 2), true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-500';
      case 'starting':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getLogTypeColor = (type: string) => {
    switch (type) {
      case 'stderr':
        return 'text-red-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!project) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <p>Invalid Streamlit project format</p>
      </div>
    );
  }

  const fileList = Object.keys(project.files);
  const currentFileContent = selectedFile ? project.files[selectedFile] || '' : '';

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h3 className="text-lg font-semibold">{project.title}</h3>
          <p className="text-sm text-gray-600">{project.description}</p>
        </div>
        <div className="flex items-center gap-2">
          {metadata?.process && (
            <Badge variant="outline" className="flex items-center gap-1">
              <div className={cn('w-2 h-2 rounded-full', getStatusColor(metadata.process.status))} />
              {metadata.process.status}
            </Badge>
          )}
          {metadata?.process?.url && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(metadata.process?.url, '_blank')}
            >
              <ExternalLinkIcon size={16} />
              Open App
            </Button>
          )}
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* File Explorer */}
        <div className="w-64 border-r bg-gray-50 dark:bg-gray-900">
          <div className="p-3 border-b">
            <h4 className="text-sm font-medium">Files</h4>
          </div>
          <ScrollArea className="h-full">
            <div className="p-2">
              {fileList.map((filename) => (
                <button
                  key={filename}
                  onClick={() => setSelectedFile(filename)}
                  className={cn(
                    'w-full flex items-center gap-2 p-2 text-left text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-800',
                    selectedFile === filename && 'bg-blue-100 dark:bg-blue-900'
                  )}
                >
                  <FileIcon size={16} />
                  <span className="truncate">{filename}</span>
                  {filename === project.entryPoint && (
                    <Badge variant="secondary" className="text-xs">
                      main
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Code Editor */}
          <div className={cn('flex-1', showPreview && metadata?.process?.url ? 'h-1/2' : 'h-full')}>
            <div className="h-full border-b">
              <div className="flex items-center justify-between p-2 border-b bg-gray-50 dark:bg-gray-900">
                <span className="text-sm font-medium">{selectedFile}</span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                  >
                    {showPreview ? 'Hide Preview' : 'Show Preview'}
                  </Button>
                </div>
              </div>
              <div className="h-full">
                <CodeEditor
                  content={currentFileContent}
                  currentVersionIndex={currentVersionIndex}
                  isCurrentVersion={isCurrentVersion}
                  onSaveContent={handleFileContentChange}
                  status={status}
                />
              </div>
            </div>
          </div>

          {/* Preview and Logs */}
          {showPreview && (
            <div className="h-1/2 flex">
              {/* App Preview */}
              {metadata?.process?.url && (
                <div className="flex-1 border-r">
                  <div className="p-2 border-b bg-gray-50 dark:bg-gray-900">
                    <span className="text-sm font-medium">App Preview</span>
                  </div>
                  <iframe
                    src={metadata.process.url}
                    className="w-full h-full border-0"
                    title="Streamlit App Preview"
                  />
                </div>
              )}

              {/* Logs */}
              <div className="w-80">
                <div className="p-2 border-b bg-gray-50 dark:bg-gray-900">
                  <span className="text-sm font-medium">Logs</span>
                </div>
                <ScrollArea className="h-full">
                  <div className="p-2 space-y-1">
                    {metadata?.logs.map((log) => (
                      <div key={log.id} className="text-xs">
                        <span className="text-gray-500">
                          {log.timestamp.toLocaleTimeString()}
                        </span>
                        <span className={cn('ml-2', getLogTypeColor(log.type))}>
                          [{log.type}]
                        </span>
                        <span className="ml-1">{log.message}</span>
                      </div>
                    ))}
                    {(!metadata?.logs || metadata.logs.length === 0) && (
                      <p className="text-gray-500 text-xs">No logs yet</p>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
